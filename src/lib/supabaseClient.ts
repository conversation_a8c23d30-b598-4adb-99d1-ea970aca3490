import { createClient } from '@supabase/supabase-js';
import { browser } from '$app/environment';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from './env.js';

/**
 * Enhanced Supabase Client with Initialization Tracking
 * Following Code Complete: Defensive programming, proper initialization
 */

// Client initialization state
let clientInitialized = false;
let initializationPromise: Promise<void> | null = null;

// Create Supabase client with enhanced configuration
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
	auth: {
		// Reduce initial session check timeout for faster startup
		detectSessionInUrl: true,
		persistSession: true,
		autoRefreshToken: true,
		// Add storage key for better session management
		storageKey: 'work-scheduler-auth'
	},
	// Add global timeout configuration
	global: {
		headers: {
			'X-Client-Info': 'work-scheduler-web'
		}
	}
});

/**
 * Initialize Supabase client and wait for it to be ready
 * Following Code Complete: Explicit initialization, error handling
 */
export async function initializeSupabaseClient(): Promise<void> {
	// Return existing promise if already initializing
	if (initializationPromise) {
		return initializationPromise;
	}

	// Return immediately if already initialized
	if (clientInitialized) {
		return Promise.resolve();
	}

	// Create initialization promise
	initializationPromise = new Promise<void>(async (resolve, reject) => {
		try {
			// Only initialize in browser environment
			if (!browser) {
				clientInitialized = true;
				resolve();
				return;
			}

			console.log('🔧 Supabase: Initializing client...');

			// Wait for initial session check with progressive timeout
			const timeouts = [2000, 5000, 10000]; // 2s, 5s, 10s
			let lastError: Error | null = null;

			for (const timeout of timeouts) {
				try {
					const timeoutPromise = new Promise<never>((_, reject) => {
						setTimeout(() => {
							reject(new Error(`Supabase initialization timed out after ${timeout}ms`));
						}, timeout);
					});

					const sessionPromise = supabase.auth.getSession();
					await Promise.race([sessionPromise, timeoutPromise]);

					// If we get here, the client is responsive
					console.log('✅ Supabase: Client initialized successfully');
					clientInitialized = true;
					resolve();
					return;

				} catch (error) {
					lastError = error instanceof Error ? error : new Error('Unknown initialization error');
					console.warn(`⚠️ Supabase: Initialization attempt failed (${timeout}ms):`, lastError.message);

					// Wait a bit before retrying
					if (timeout < timeouts[timeouts.length - 1]) {
						await new Promise(resolve => setTimeout(resolve, 1000));
					}
				}
			}

			// All attempts failed
			console.error('❌ Supabase: Client initialization failed after all attempts');
			clientInitialized = false;
			reject(lastError || new Error('Supabase client initialization failed'));

		} catch (error) {
			console.error('❌ Supabase: Unexpected initialization error:', error);
			clientInitialized = false;
			reject(error);
		}
	});

	return initializationPromise;
}

/**
 * Check if Supabase client is ready for use
 * Following Code Complete: Clear state checking
 */
export function isSupabaseClientReady(): boolean {
	return clientInitialized;
}

/**
 * Get Supabase client with initialization check
 * Following Code Complete: Defensive programming, clear error messages
 */
export function getSupabaseClient() {
	if (!browser) {
		// In SSR context, return client directly
		return supabase;
	}

	if (!clientInitialized) {
		console.warn('⚠️ Supabase: Client accessed before initialization. Call initializeSupabaseClient() first.');
	}

	return supabase;
}