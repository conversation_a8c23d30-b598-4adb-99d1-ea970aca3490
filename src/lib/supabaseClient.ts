import { createClient } from '@supabase/supabase-js';
import { browser } from '$app/environment';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from './env.js';

/**
 * Enhanced Supabase Client with Initialization Tracking
 * Following Code Complete: Defensive programming, proper initialization
 */

// Client initialization state
let clientInitialized = false;
let initializationPromise: Promise<void> | null = null;

// Create Supabase client with enhanced configuration
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
	auth: {
		// Reduce initial session check timeout for faster startup
		detectSessionInUrl: true,
		persistSession: true,
		autoRefreshToken: true,
		// Add storage key for better session management
		storageKey: 'work-scheduler-auth'
	},
	// Add global timeout configuration
	global: {
		headers: {
			'X-Client-Info': 'work-scheduler-web'
		}
	}
});

/**
 * Initialize Supabase client with simple readiness check
 * Following Code Complete: Explicit initialization, avoid circular dependencies
 */
export async function initializeSupabaseClient(): Promise<void> {
	// Return existing promise if already initializing
	if (initializationPromise) {
		return initializationPromise;
	}

	// Return immediately if already initialized
	if (clientInitialized) {
		return Promise.resolve();
	}

	// Create initialization promise
	initializationPromise = new Promise<void>(async (resolve, reject) => {
		try {
			// Only initialize in browser environment
			if (!browser) {
				clientInitialized = true;
				resolve();
				return;
			}

			console.log('🔧 Supabase: Initializing client...');

			// Simple approach: just wait for the client to be ready
			// without calling getSession() which can cause circular dependencies

			// Wait for DOM to be ready first
			if (document.readyState !== 'complete') {
				await new Promise<void>((resolve) => {
					if (document.readyState === 'complete') {
						resolve();
					} else {
						const handler = () => {
							if (document.readyState === 'complete') {
								document.removeEventListener('readystatechange', handler);
								resolve();
							}
						};
						document.addEventListener('readystatechange', handler);
					}
				});
			}

			// Give the client a moment to initialize its internal state
			await new Promise(resolve => setTimeout(resolve, 100));

			// Mark as initialized - the actual session validation will happen later
			console.log('✅ Supabase: Client initialized successfully');
			clientInitialized = true;
			resolve();

		} catch (error) {
			console.error('❌ Supabase: Unexpected initialization error:', error);
			clientInitialized = false;
			reject(error);
		}
	});

	return initializationPromise;
}

/**
 * Check if Supabase client is ready for use
 * Following Code Complete: Clear state checking
 */
export function isSupabaseClientReady(): boolean {
	return clientInitialized;
}

/**
 * Get Supabase client with initialization check
 * Following Code Complete: Defensive programming, clear error messages
 */
export function getSupabaseClient() {
	if (!browser) {
		// In SSR context, return client directly
		return supabase;
	}

	if (!clientInitialized) {
		console.warn('⚠️ Supabase: Client accessed before initialization. Call initializeSupabaseClient() first.');
	}

	return supabase;
}