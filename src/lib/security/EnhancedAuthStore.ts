/**
 * Enhanced Authentication Store
 * Integrates all security services for comprehensive authentication management
 * Following Code Complete principles: Centralized security, comprehensive integration
 */

import { writable, derived, get } from 'svelte/store';
import { supabase } from '$lib/supabaseClient.js';
import { validateSecurity, type SecurityValidationResult } from './TokenValidationService.js';
import { sessionManager, type SessionState } from './SessionManager.js';
import { securityAudit } from './SecurityAuditService.js';
import { secureRPC } from './SecureRPCService.js';
import type { User, Session } from '@supabase/supabase-js';

// ============================================================================
// TYPES
// ============================================================================

export interface EnhancedAuthState {
	// Core authentication
	isAuthenticated: boolean;
	isLoading: boolean;
	user: User | null;
	session: Session | null;
	
	// Organization context
	organizationId: string | null;
	userRole: string | null;
	membershipActive: boolean;
	needsRestaurantSetup: boolean;
	
	// Security status
	securityStatus: 'secure' | 'warning' | 'error';
	sessionTimeRemaining: number;
	needsRefresh: boolean;
	lastValidation: number;
	
	// Error handling
	error: string | null;
	lastError: string | null;
	retryCount: number;
}

export interface AuthenticationOptions {
	email: string;
	password: string;
	rememberMe?: boolean;
}

export interface AuthenticationResult {
	success: boolean;
	error?: string;
	requiresVerification?: boolean;
	user?: User;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialAuthState: EnhancedAuthState = {
	isAuthenticated: false,
	isLoading: true,
	user: null,
	session: null,
	organizationId: null,
	userRole: null,
	membershipActive: false,
	needsRestaurantSetup: false,
	securityStatus: 'error',
	sessionTimeRemaining: 0,
	needsRefresh: false,
	lastValidation: 0,
	error: null,
	lastError: null,
	retryCount: 0
};

// ============================================================================
// STORES
// ============================================================================

export const enhancedAuthState = writable<EnhancedAuthState>(initialAuthState);

// Derived stores for convenience
export const isAuthenticated = derived(enhancedAuthState, $state => $state.isAuthenticated);
export const isLoading = derived(enhancedAuthState, $state => $state.isLoading);
export const currentUser = derived(enhancedAuthState, $state => $state.user);
export const organizationContext = derived(enhancedAuthState, $state => ({
	organizationId: $state.organizationId,
	userRole: $state.userRole,
	membershipActive: $state.membershipActive
}));
export const securityStatus = derived(enhancedAuthState, $state => $state.securityStatus);

// ============================================================================
// ENHANCED AUTH SERVICE
// ============================================================================

export class EnhancedAuthService {
	private validationTimer: NodeJS.Timeout | null = null;
	private initialized = false;

	constructor() {
		this.setupSessionListener();
		this.setupSecurityMonitoring();
	}

	/**
	 * Initialize enhanced authentication system
	 * Following Code Complete: Comprehensive initialization, error handling
	 */
	async initialize(): Promise<void> {
		if (this.initialized) return;

		console.log('🔒 EnhancedAuthService: Initializing...');
		
		try {
			enhancedAuthState.update(state => ({ ...state, isLoading: true, error: null }));

			// Initialize session manager
			await sessionManager.initialize();

			// Validate current security context
			await this.validateAndUpdateState();

			// Start periodic validation
			this.startPeriodicValidation();

			this.initialized = true;
			console.log('✅ EnhancedAuthService: Initialized successfully');

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Initialization failed';
			console.error('🔒 EnhancedAuthService: Initialization error:', errorMessage);
			
			securityAudit.logEvent('security_violation', {
				error: errorMessage,
				context: 'initialization'
			}, 'high');

			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage,
				securityStatus: 'error'
			}));
		}
	}

	/**
	 * Sign in with enhanced security validation
	 * Following Code Complete: Secure authentication, comprehensive logging
	 */
	async signIn(options: AuthenticationOptions): Promise<AuthenticationResult> {
		console.log('🔒 EnhancedAuthService: Signing in...');
		
		try {
			enhancedAuthState.update(state => ({ ...state, isLoading: true, error: null }));

			// Attempt authentication
			const { data, error } = await supabase.auth.signInWithPassword({
				email: options.email,
				password: options.password
			});

			if (error) {
				// Log authentication failure
				securityAudit.logAuthFailure(options.email, error.message);
				
				enhancedAuthState.update(state => ({
					...state,
					isLoading: false,
					error: error.message,
					lastError: error.message,
					retryCount: state.retryCount + 1
				}));

				return { success: false, error: error.message };
			}

			if (!data.user || !data.session) {
				const errorMsg = 'No user data returned';
				securityAudit.logAuthFailure(options.email, errorMsg);
				
				enhancedAuthState.update(state => ({
					...state,
					isLoading: false,
					error: errorMsg
				}));

				return { success: false, error: errorMsg };
			}

			// Log successful authentication
			securityAudit.logAuthSuccess(data.user.id);

			// Validate and update state
			await this.validateAndUpdateState();

			console.log('✅ EnhancedAuthService: Sign in successful');
			return { success: true, user: data.user };

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
			console.error('🔒 EnhancedAuthService: Sign in error:', errorMessage);
			
			securityAudit.logEvent('authentication_failure', {
				email: options.email,
				error: errorMessage
			}, 'medium');

			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage,
				lastError: errorMessage
			}));

			return { success: false, error: errorMessage };
		}
	}

	/**
	 * Sign up with enhanced validation
	 * Following Code Complete: Secure registration, validation
	 */
	async signUp(options: AuthenticationOptions): Promise<AuthenticationResult> {
		console.log('🔒 EnhancedAuthService: Signing up...');
		
		try {
			enhancedAuthState.update(state => ({ ...state, isLoading: true, error: null }));

			const { data, error } = await supabase.auth.signUp({
				email: options.email,
				password: options.password
			});

			if (error) {
				securityAudit.logEvent('authentication_failure', {
					email: options.email,
					error: error.message,
					type: 'signup'
				}, 'medium');

				enhancedAuthState.update(state => ({
					...state,
					isLoading: false,
					error: error.message
				}));

				return { success: false, error: error.message };
			}

			// Log successful signup
			if (data.user) {
				securityAudit.logEvent('authentication_success', {
					userId: data.user.id,
					type: 'signup'
				}, 'low', data.user.id);
			}

			enhancedAuthState.update(state => ({ ...state, isLoading: false }));

			return {
				success: true,
				requiresVerification: !data.session,
				user: data.user || undefined
			};

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Sign up failed';
			console.error('🔒 EnhancedAuthService: Sign up error:', errorMessage);
			
			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage
			}));

			return { success: false, error: errorMessage };
		}
	}

	/**
	 * Sign out with comprehensive cleanup
	 * Following Code Complete: Secure logout, complete state clearing
	 */
	async signOut(): Promise<void> {
		console.log('🔒 EnhancedAuthService: Signing out...');
		
		const currentState = get(enhancedAuthState);
		
		try {
			// Log session end
			if (currentState.user) {
				securityAudit.logEvent('session_end', {
					userId: currentState.user.id,
					reason: 'user_logout'
				}, 'low', currentState.user.id, currentState.organizationId);
			}

			// End session through session manager
			await sessionManager.endSession('user_logout');

			// Sign out from Supabase
			await supabase.auth.signOut();

			// Clear all state
			this.clearAuthState();

			console.log('✅ EnhancedAuthService: Sign out successful');

		} catch (error) {
			console.error('🔒 EnhancedAuthService: Sign out error:', error);
			// Force clear state even if signout fails
			this.clearAuthState();
		}
	}

	/**
	 * Validate and update authentication state
	 * Following Code Complete: Comprehensive validation, state synchronization
	 */
	async validateAndUpdateState(): Promise<void> {
		try {
			const validation = await validateSecurity();
			const sessionStatus = sessionManager.getStatus();

			const newState: Partial<EnhancedAuthState> = {
				isAuthenticated: validation.isValid,
				isLoading: false,
				user: validation.user,
				session: validation.session,
				organizationId: validation.organizationContext.organizationId,
				userRole: validation.organizationContext.userRole,
				membershipActive: validation.organizationContext.membershipActive,
				needsRestaurantSetup: validation.isValid && !validation.organizationContext.organizationId,
				securityStatus: this.getSecurityStatus(validation),
				sessionTimeRemaining: sessionStatus.timeRemaining,
				needsRefresh: validation.needsRefresh,
				lastValidation: Date.now(),
				error: validation.error || null,
				retryCount: 0
			};

			enhancedAuthState.update(state => ({ ...state, ...newState }));

		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Validation failed';
			console.error('🔒 EnhancedAuthService: Validation error:', errorMessage);
			
			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: errorMessage,
				securityStatus: 'error'
			}));
		}
	}

	/**
	 * Handle auth state changes from Supabase
	 * Following Code Complete: Event-driven architecture, progressive enhancement
	 */
	async handleAuthStateChange(event: string, session: Session | null): Promise<void> {
		console.log(`🔒 EnhancedAuthService: Handling auth state change: ${event}`);

		try {
			// Initialize if not already done
			if (!this.initialized) {
				await this.initialize();
			}

			// Update state based on the auth event
			switch (event) {
				case 'INITIAL_SESSION':
				case 'SIGNED_IN':
				case 'TOKEN_REFRESHED':
					if (session) {
						console.log('✅ EnhancedAuthService: Valid session detected, updating state...');
						await this.validateAndUpdateState();
					} else {
						console.log('🔒 EnhancedAuthService: No session in auth event, clearing state...');
						this.clearAuthState();
					}
					break;

				case 'SIGNED_OUT':
					console.log('🔒 EnhancedAuthService: User signed out, clearing state...');
					this.clearAuthState();
					break;

				default:
					console.log(`🔒 EnhancedAuthService: Unhandled auth event: ${event}`);
					break;
			}

		} catch (error) {
			console.error('🔒 EnhancedAuthService: Auth state change handling failed:', error);

			// On error, ensure we're in a safe state
			enhancedAuthState.update(state => ({
				...state,
				isLoading: false,
				error: error instanceof Error ? error.message : 'Auth state change failed'
			}));
		}
	}

	/**
	 * Check if user has required role
	 * Following Code Complete: Authorization validation, role-based access
	 */
	hasRole(requiredRole: 'viewer' | 'editor' | 'admin' | 'superadmin'): boolean {
		const state = get(enhancedAuthState);
		
		if (!state.isAuthenticated || !state.organizationId || !state.membershipActive) {
			return false;
		}

		const roleHierarchy = ['viewer', 'editor', 'admin', 'superadmin'];
		const userRoleIndex = roleHierarchy.indexOf(state.userRole || '');
		const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

		return userRoleIndex >= requiredRoleIndex;
	}

	/**
	 * Execute secure RPC call with authentication
	 * Following Code Complete: Secure API access, integrated authentication
	 */
	async secureCall<T = any>(
		functionName: string,
		parameters: any = {},
		requireOrganization = false,
		requiredRole: 'viewer' | 'editor' | 'admin' | 'superadmin' = 'viewer'
	): Promise<{ data: T | null; error: any; success: boolean }> {
		const result = await secureRPC<T>(functionName, parameters, {
			requireOrganization,
			requiredRole
		});

		// Log data access
		const state = get(enhancedAuthState);
		securityAudit.logEvent('data_access', {
			function: functionName,
			success: !result.error,
			organizationRequired: requireOrganization,
			roleRequired: requiredRole
		}, 'low', state.user?.id, state.organizationId);

		return {
			data: result.data,
			error: result.error,
			success: !result.error
		};
	}

	// ============================================================================
	// PRIVATE METHODS
	// ============================================================================

	/**
	 * Setup session state listener
	 * Following Code Complete: Event-driven architecture, state synchronization
	 */
	private setupSessionListener(): void {
		sessionManager.addEventListener((event) => {
			switch (event.type) {
				case 'session_start':
				case 'session_refresh':
					this.validateAndUpdateState();
					break;
				
				case 'session_end':
				case 'session_expired':
					this.clearAuthState();
					break;
			}
		});
	}

	/**
	 * Setup security monitoring
	 * Following Code Complete: Security monitoring, event handling
	 */
	private setupSecurityMonitoring(): void {
		securityAudit.addAlertListener((alert) => {
			if (alert.severity === 'critical') {
				console.error('🚨 Critical Security Alert:', alert.message);
				// In production, could trigger additional security measures
			}
		});
	}

	/**
	 * Start periodic validation
	 * Following Code Complete: Proactive validation, automated monitoring
	 */
	private startPeriodicValidation(): void {
		if (this.validationTimer) {
			clearInterval(this.validationTimer);
		}

		this.validationTimer = setInterval(() => {
			const state = get(enhancedAuthState);
			if (state.isAuthenticated) {
				this.validateAndUpdateState();
			}
		}, 60000); // Validate every minute
	}

	/**
	 * Get security status based on validation
	 * Following Code Complete: Status assessment, clear categorization
	 */
	private getSecurityStatus(validation: SecurityValidationResult): 'secure' | 'warning' | 'error' {
		if (!validation.isValid) {
			return 'error';
		}

		if (validation.needsRefresh || validation.timeUntilExpiry < 5 * 60 * 1000) {
			return 'warning';
		}

		return 'secure';
	}

	/**
	 * Clear authentication state
	 * Following Code Complete: Complete state cleanup, security
	 */
	private clearAuthState(): void {
		enhancedAuthState.set({
			...initialAuthState,
			isLoading: false
		});

		if (this.validationTimer) {
			clearInterval(this.validationTimer);
			this.validationTimer = null;
		}
	}
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

/**
 * Global enhanced authentication service instance
 * Following Code Complete: Singleton pattern, centralized authentication
 */
export const enhancedAuth = new EnhancedAuthService();
