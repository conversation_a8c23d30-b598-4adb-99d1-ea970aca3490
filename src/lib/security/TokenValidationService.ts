/**
 * Token Validation Service
 * Centralized token management and validation for Supabase Auth
 * Following Code Complete principles: Single responsibility, comprehensive error handling
 */

import { supabase } from '$lib/supabaseClient.js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '$lib/env.js';
import type { Session, User } from '@supabase/supabase-js';

// ============================================================================
// TYPES
// ============================================================================

export interface TokenValidationResult {
	isValid: boolean;
	needsRefresh: boolean;
	timeUntilExpiry: number;
	user: User | null;
	session: Session | null;
	error?: string;
}

export interface OrganizationContext {
	organizationId: string | null;
	userRole: string | null;
	membershipActive: boolean;
}

export interface SecurityValidationResult extends TokenValidationResult {
	organizationContext: OrganizationContext;
}

// ============================================================================
// CONFIGURATION
// ============================================================================

/**
 * Token validation configuration
 * Following Code Complete: Centralized configuration, clear constants
 */
const TOKEN_CONFIG = {
	refreshThreshold: 5 * 60 * 1000, // 5 minutes before expiry
	maxRetries: 3,
	retryDelay: 1000, // 1 second
	validationTimeout: 5000, // 5 seconds (reduced for faster failure)
	fallbackTimeout: 2000, // 2 seconds for fallback attempts
	initializationTimeout: 30000 // 30 seconds for client initialization
};

// ============================================================================
// CORE TOKEN VALIDATION
// ============================================================================

/**
 * Test Supabase connectivity
 * Following Code Complete: Diagnostic function, clear error reporting
 */
async function testSupabaseConnectivity(): Promise<boolean> {
	try {
		console.log('🔧 TokenValidationService: Testing Supabase connectivity...');

		// Simple connectivity test with very short timeout
		const testPromise = new Promise<boolean>((resolve) => {
			// Try to access the Supabase URL directly
			fetch(`${SUPABASE_URL}/rest/v1/`, {
				method: 'HEAD',
				headers: {
					'apikey': SUPABASE_ANON_KEY,
					'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
				}
			})
			.then(() => {
				console.log('✅ TokenValidationService: Supabase connectivity OK');
				resolve(true);
			})
			.catch((error) => {
				console.warn('⚠️ TokenValidationService: Supabase connectivity failed:', error.message);
				resolve(false);
			});
		});

		const timeoutPromise = new Promise<boolean>((resolve) => {
			setTimeout(() => {
				console.warn('⚠️ TokenValidationService: Connectivity test timed out');
				resolve(false);
			}, TOKEN_CONFIG.fallbackTimeout);
		});

		return await Promise.race([testPromise, timeoutPromise]);
	} catch (error) {
		console.warn('⚠️ TokenValidationService: Connectivity test failed:', error);
		return false;
	}
}



/**
 * Validate current session token with robust timeout handling and fallback
 * Following Code Complete: Pure function, comprehensive validation, graceful degradation
 */
export async function validateToken(): Promise<TokenValidationResult> {
	const defaultResult: TokenValidationResult = {
		isValid: false,
		needsRefresh: false,
		timeUntilExpiry: 0,
		user: null,
		session: null
	};

	try {
		console.log('🔒 TokenValidationService: Validating session token...');

		// Skip connectivity test for now and try direct validation
		// If it fails, we'll handle it gracefully
		console.log('🔒 TokenValidationService: Attempting direct session validation...');

		// Try with a very short timeout to fail fast
		const shortTimeoutPromise = new Promise<never>((_, reject) => {
			setTimeout(() => {
				reject(new Error(`Token validation timed out (${TOKEN_CONFIG.validationTimeout}ms)`));
			}, TOKEN_CONFIG.validationTimeout);
		});

		try {
			// First attempt with short timeout
			console.log('🔒 TokenValidationService: Calling supabase.auth.getSession()...');
			const sessionPromise = supabase.auth.getSession();
			const { data: { session }, error } = await Promise.race([sessionPromise, shortTimeoutPromise]);
			console.log('🔒 TokenValidationService: getSession() completed successfully');

			if (error) {
				console.warn('🔒 TokenValidationService: Session error:', error.message);
				return { ...defaultResult, error: error.message };
			}

			if (!session?.user) {
				console.log('🔒 TokenValidationService: No active session found');
				return { ...defaultResult, error: 'No active session' };
			}

			// Calculate token expiration
			const currentTime = Date.now();
			const expiryTime = session.expires_at ? session.expires_at * 1000 : 0;
			const timeUntilExpiry = expiryTime - currentTime;

			// Check if token is expired
			if (timeUntilExpiry <= 0) {
				console.warn('🔒 TokenValidationService: Token expired');
				return { ...defaultResult, error: 'Token expired' };
			}

			// Check if token needs refresh
			const needsRefresh = timeUntilExpiry < TOKEN_CONFIG.refreshThreshold;

			console.log('✅ TokenValidationService: Token validation successful');
			return {
				isValid: true,
				needsRefresh,
				timeUntilExpiry,
				user: session.user,
				session
			};

		} catch (error) {
			// If first attempt times out, try to handle gracefully
			console.warn('⚠️ TokenValidationService: Session check failed:', error instanceof Error ? error.message : 'Unknown error');

			// Return graceful failure - let the app continue without authentication
			console.log('🔒 TokenValidationService: Graceful failure - continuing without authentication');
			return { ...defaultResult, error: 'Session validation unavailable' };
		}

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : 'Unknown validation error';
		console.error('❌ TokenValidationService: Validation failed:', errorMessage);
		return { ...defaultResult, error: errorMessage };
	}
}

/**
 * Refresh session token if needed
 * Following Code Complete: Defensive programming, retry logic
 */
export async function refreshTokenIfNeeded(): Promise<TokenValidationResult> {
	const validation = await validateToken();

	if (!validation.isValid) {
		return validation;
	}

	if (!validation.needsRefresh) {
		return validation;
	}

	console.log('🔒 TokenValidationService: Refreshing token...');

	try {
		const { data: { session }, error } = await supabase.auth.refreshSession();

		if (error) {
			console.error('🔒 TokenValidationService: Refresh failed:', error.message);
			return { ...validation, error: error.message };
		}

		if (!session?.user) {
			return { ...validation, error: 'No session after refresh' };
		}

		console.log('✅ TokenValidationService: Token refreshed successfully');

		// Return updated validation result
		const currentTime = Date.now();
		const expiryTime = session.expires_at ? session.expires_at * 1000 : 0;
		const timeUntilExpiry = expiryTime - currentTime;

		return {
			isValid: true,
			needsRefresh: false,
			timeUntilExpiry,
			user: session.user,
			session
		};

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : 'Unknown refresh error';
		console.error('🔒 TokenValidationService: Refresh error:', errorMessage);
		return { ...validation, error: errorMessage };
	}
}

// ============================================================================
// ORGANIZATION CONTEXT VALIDATION
// ============================================================================

/**
 * Get user's organization context
 * Following Code Complete: Clear business logic, error handling
 */
export async function getOrganizationContext(): Promise<OrganizationContext> {
	const defaultContext: OrganizationContext = {
		organizationId: null,
		userRole: null,
		membershipActive: false
	};

	try {
		const { data: organizations, error } = await supabase.rpc('get_user_organizations');

		if (error) {
			console.warn('🔒 TokenValidationService: Organization context error:', error.message);
			return defaultContext;
		}

		if (!organizations || organizations.length === 0) {
			console.log('🔒 TokenValidationService: No organizations found for user');
			return defaultContext;
		}

		// Use the first active organization
		const activeOrg = organizations.find((org: any) => org.membership_active) || organizations[0];

		return {
			organizationId: activeOrg.id,
			userRole: activeOrg.user_role,
			membershipActive: activeOrg.membership_active ?? true
		};

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : 'Unknown organization error';
		console.error('🔒 TokenValidationService: Organization context failed:', errorMessage);
		return defaultContext;
	}
}

/**
 * Comprehensive security validation
 * Combines token validation with organization context
 * Following Code Complete: Comprehensive validation, single entry point
 */
export async function validateSecurity(): Promise<SecurityValidationResult> {
	// First validate the token
	const tokenValidation = await refreshTokenIfNeeded();

	// Get organization context if authenticated
	let organizationContext: OrganizationContext = {
		organizationId: null,
		userRole: null,
		membershipActive: false
	};

	if (tokenValidation.isValid) {
		organizationContext = await getOrganizationContext();
	}

	return {
		...tokenValidation,
		organizationContext
	};
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if user has specific role in current organization
 * Following Code Complete: Clear business rule validation
 */
export function hasOrganizationRole(
	context: OrganizationContext,
	requiredRole: 'viewer' | 'editor' | 'admin' | 'superadmin'
): boolean {
	if (!context.organizationId || !context.membershipActive) {
		return false;
	}

	const roleHierarchy = ['viewer', 'editor', 'admin', 'superadmin'];
	const userRoleIndex = roleHierarchy.indexOf(context.userRole || '');
	const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

	return userRoleIndex >= requiredRoleIndex;
}

/**
 * Format time until expiry for display
 * Following Code Complete: User-friendly formatting
 */
export function formatTimeUntilExpiry(timeUntilExpiry: number): string {
	if (timeUntilExpiry <= 0) {
		return 'Expired';
	}

	const minutes = Math.floor(timeUntilExpiry / (1000 * 60));
	const hours = Math.floor(minutes / 60);

	if (hours > 0) {
		return `${hours}h ${minutes % 60}m`;
	}

	return `${minutes}m`;
}

/**
 * Get security status summary
 * Following Code Complete: Clear status reporting
 */
export function getSecurityStatus(validation: SecurityValidationResult): {
	status: 'secure' | 'warning' | 'error';
	message: string;
	details: any;
} {
	if (!validation.isValid) {
		return {
			status: 'error',
			message: validation.error || 'Authentication failed',
			details: { hasSession: !!validation.session }
		};
	}

	if (validation.needsRefresh) {
		return {
			status: 'warning',
			message: 'Session will expire soon',
			details: {
				timeUntilExpiry: formatTimeUntilExpiry(validation.timeUntilExpiry),
				hasOrganization: !!validation.organizationContext.organizationId
			}
		};
	}

	return {
		status: 'secure',
		message: 'Authentication valid',
		details: {
			timeUntilExpiry: formatTimeUntilExpiry(validation.timeUntilExpiry),
			organizationId: validation.organizationContext.organizationId,
			userRole: validation.organizationContext.userRole
		}
	};
}
