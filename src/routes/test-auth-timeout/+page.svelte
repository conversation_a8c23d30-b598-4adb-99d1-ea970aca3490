<script lang="ts">
	/**
	 * Authentication Timeout Test Page
	 * Tests the fixed authentication initialization without timeout errors
	 */
	
	import { onMount } from 'svelte';
	import { validateSecurity, initializeSecurity } from '$lib/security/index.js';
	import { initializeSupabaseClient, isSupabaseClientReady } from '$lib/supabaseClient.js';
	
	let testResults: Array<{
		test: string;
		status: 'pending' | 'success' | 'error';
		message: string;
		duration?: number;
	}> = [];
	
	let isRunning = false;
	
	async function runTests() {
		isRunning = true;
		testResults = [];
		
		// Test 1: Supabase Client Initialization
		await runTest('Supabase Client Initialization', async () => {
			const start = Date.now();
			await initializeSupabaseClient();
			const duration = Date.now() - start;
			
			if (isSupabaseClientReady()) {
				return { success: true, message: `Client initialized in ${duration}ms`, duration };
			} else {
				return { success: false, message: 'Client not ready after initialization', duration };
			}
		});
		
		// Test 2: Security Initialization
		await runTest('Security System Initialization', async () => {
			const start = Date.now();
			await initializeSecurity();
			const duration = Date.now() - start;
			return { success: true, message: `Security initialized in ${duration}ms`, duration };
		});
		
		// Test 3: Token Validation
		await runTest('Token Validation', async () => {
			const start = Date.now();
			const result = await validateSecurity();
			const duration = Date.now() - start;
			
			if (result.error && result.error.includes('timed out')) {
				return { success: false, message: `Validation timed out: ${result.error}`, duration };
			} else {
				return { success: true, message: `Validation completed in ${duration}ms (valid: ${result.isValid})`, duration };
			}
		});
		
		// Test 4: Multiple Rapid Validations
		await runTest('Multiple Rapid Validations', async () => {
			const start = Date.now();
			const promises = Array(5).fill(0).map(() => validateSecurity());
			const results = await Promise.all(promises);
			const duration = Date.now() - start;
			
			const timeouts = results.filter(r => r.error && r.error.includes('timed out'));
			if (timeouts.length > 0) {
				return { success: false, message: `${timeouts.length}/5 validations timed out`, duration };
			} else {
				return { success: true, message: `All 5 validations completed in ${duration}ms`, duration };
			}
		});
		
		isRunning = false;
	}
	
	async function runTest(testName: string, testFn: () => Promise<{ success: boolean; message: string; duration?: number }>) {
		// Add pending test
		testResults = [...testResults, { test: testName, status: 'pending', message: 'Running...' }];
		
		try {
			const result = await testFn();
			
			// Update with result
			testResults = testResults.map(t => 
				t.test === testName 
					? { 
						test: testName, 
						status: result.success ? 'success' : 'error', 
						message: result.message,
						duration: result.duration
					}
					: t
			);
		} catch (error) {
			// Update with error
			testResults = testResults.map(t => 
				t.test === testName 
					? { 
						test: testName, 
						status: 'error', 
						message: error instanceof Error ? error.message : 'Unknown error'
					}
					: t
			);
		}
	}
	
	onMount(() => {
		// Auto-run tests on page load
		runTests();
	});
</script>

<div class="container mx-auto p-6 max-w-4xl">
	<h1 class="text-3xl font-bold mb-6">Authentication Timeout Test</h1>
	
	<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
		<h2 class="text-lg font-semibold text-blue-800 mb-2">Test Purpose</h2>
		<p class="text-blue-700">
			This page tests the fixed authentication initialization to ensure there are no more timeout errors.
			The tests verify that Supabase client initialization, security validation, and token validation 
			all complete within reasonable timeframes without circular dependencies.
		</p>
	</div>
	
	<div class="flex gap-4 mb-6">
		<button 
			on:click={runTests}
			disabled={isRunning}
			class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
		>
			{isRunning ? 'Running Tests...' : 'Run Tests'}
		</button>
		
		<button 
			on:click={() => testResults = []}
			disabled={isRunning}
			class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
		>
			Clear Results
		</button>
	</div>
	
	<div class="space-y-4">
		{#each testResults as result}
			<div class="border rounded-lg p-4 {
				result.status === 'success' ? 'border-green-200 bg-green-50' :
				result.status === 'error' ? 'border-red-200 bg-red-50' :
				'border-yellow-200 bg-yellow-50'
			}">
				<div class="flex items-center justify-between">
					<h3 class="font-semibold {
						result.status === 'success' ? 'text-green-800' :
						result.status === 'error' ? 'text-red-800' :
						'text-yellow-800'
					}">
						{result.test}
					</h3>
					
					<div class="flex items-center gap-2">
						{#if result.duration}
							<span class="text-sm text-gray-600">{result.duration}ms</span>
						{/if}
						
						<span class="px-2 py-1 rounded text-xs font-medium {
							result.status === 'success' ? 'bg-green-100 text-green-800' :
							result.status === 'error' ? 'bg-red-100 text-red-800' :
							'bg-yellow-100 text-yellow-800'
						}">
							{result.status === 'success' ? '✅ PASS' :
							 result.status === 'error' ? '❌ FAIL' :
							 '⏳ RUNNING'}
						</span>
					</div>
				</div>
				
				<p class="mt-2 text-sm {
					result.status === 'success' ? 'text-green-700' :
					result.status === 'error' ? 'text-red-700' :
					'text-yellow-700'
				}">
					{result.message}
				</p>
			</div>
		{/each}
		
		{#if testResults.length === 0}
			<div class="text-center text-gray-500 py-8">
				No test results yet. Click "Run Tests" to start.
			</div>
		{/if}
	</div>
	
	<div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
		<h3 class="font-semibold text-gray-800 mb-2">Expected Results</h3>
		<ul class="text-sm text-gray-700 space-y-1">
			<li>• <strong>Supabase Client Initialization:</strong> Should complete in under 1 second</li>
			<li>• <strong>Security System Initialization:</strong> Should complete without timeout errors</li>
			<li>• <strong>Token Validation:</strong> Should complete in under 15 seconds without timeout</li>
			<li>• <strong>Multiple Rapid Validations:</strong> Should handle concurrent requests without timeouts</li>
		</ul>
	</div>
</div>
